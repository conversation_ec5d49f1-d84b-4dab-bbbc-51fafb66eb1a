import express from 'express';
// import mongoose from 'mongoose';
import { isAuthenticated } from '../middleware/auth.js';
// import League from '../models/League.js';
// import Player from '../models/Player.js';
// import Team from '../models/Team.js';
import {
    getMockLeague,
    isUserParticipant,
    getUserTeam,
    isUserTurn,
    getAvailablePlayers
} from '../utils/mockData.js';

const router = express.Router();

// Get draft status and current state
router.get('/api/draft/:leagueId/status', isAuthenticated, async (req, res) => {
    try {
        const { leagueId } = req.params;

        // Use mock data for testing
        console.log('Using mock data for draft status');

        const league = getMockLeague(leagueId);
        if (!league) {
            return res.status(404).json({
                success: false,
                error: 'League not found (mock data)'
            });
        }

        // Check if user is a participant
        if (!isUserParticipant(league, req.session.user.id)) {
            return res.status(403).json({
                success: false,
                error: 'You are not a participant in this league'
            });
        }

        const availablePlayers = getAvailablePlayers(league);
        const userTeam = getUserTeam(league, req.session.user.id);
        const userTurn = isUserTurn(league, req.session.user.id);

        return res.json({
            success: true,
            draftState: {
                isActive: league.draftState.isActive,
                currentRound: league.draftState.currentRound,
                currentPick: league.draftState.currentPick,
                currentTurnUser: league.draftState.currentTurnUserID,
                draftOrder: league.draftState.draftOrder,
                pickHistory: league.draftState.pickHistory,
                isDraftComplete: league.draftState.isDraftComplete
            },
            availablePlayers: availablePlayers,
            participants: league.participants,
            userTeam: userTeam,
            isUserTurn: userTurn,
            draftSettings: league.draftSettings
        });

    } catch (error) {
        console.error('Error fetching draft status:', error);
        res.status(500).json({
            success: false,
            error: `Error: ${error.message}`
        });
    }
});

// Make a draft pick (mock implementation)
router.post('/api/draft/:leagueId/pick', isAuthenticated, async (req, res) => {
    try {
        const { playerId } = req.body;

        if (!playerId) {
            return res.status(400).json({
                success: false,
                error: 'Player ID is required'
            });
        }

        // Mock response for testing
        res.json({
            success: true,
            message: 'Player drafted successfully (mock)',
            pick: {
                player: { name: 'Mock Player', _id: playerId },
                round: 1,
                pick: 1,
                team: 'Mock Team'
            },
            nextTurn: {
                currentRound: 1,
                currentPick: 2,
                currentTurnUserID: '507f1f77bcf86cd799439014',
                isDraftComplete: false
            }
        });
    } catch (error) {
        console.error('Error making draft pick:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Auto-pick for current user (mock implementation)
router.post('/api/draft/:leagueId/auto-pick', isAuthenticated, async (req, res) => {
    try {
        // Mock auto-pick response
        res.json({
            success: true,
            message: 'Auto-picked player successfully (mock)',
            pick: {
                player: { name: 'Auto-picked Player', _id: '507f1f77bcf86cd799439017' },
                round: 1,
                pick: 1,
                team: 'Mock Team'
            },
            nextTurn: {
                currentRound: 1,
                currentPick: 2,
                currentTurnUserID: '507f1f77bcf86cd799439014',
                isDraftComplete: false
            }
        });
    } catch (error) {
        console.error('Error with auto-pick:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});



export default router;
